#[approck::http(GET /dashboard/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(app: App, identity: Identity, doc: Document) -> Response {
        use maud::html;

        doc.set_title("Dashboard");

        // Check if user is authenticated
        let is_authenticated = identity.is_logged_in();

        doc.add_body(html! {
            div.container.mt-4 {
                // Header section
                div.row.mb-4 {
                    div.col-md-8 {
                        h1.display-4 { "Dashboard" }
                        @if is_authenticated {
                            @if let Some(name) = identity.name() {
                                p.lead { "Welcome back, " (name) "!" }
                            } @else {
                                p.lead { "Welcome back to AppCove!" }
                            }
                        } @else {
                            p.lead { "Welcome to AppCove" }
                        }
                    }
                    div.col-md-4.text-end {
                        @if is_authenticated {
                            div.d-grid.gap-2 {
                                a.btn.btn-outline-primary href="/myaccount/" {
                                    i.fas.fa-user {}
                                    " My Account"
                                }
                                a.btn.btn-outline-secondary href="/auth/logout" {
                                    i.fas.fa-sign-out-alt {}
                                    " Sign Out"
                                }
                            }
                        } @else {
                            div.d-grid.gap-2 {
                                a.btn.btn-primary.btn-lg href="/auth/" {
                                    i.fas.fa-sign-in-alt {}
                                    " Sign In"
                                }
                                a.btn.btn-outline-secondary href="/" {
                                    i.fas.fa-home {}
                                    " Home"
                                }
                            }
                        }
                    }
                }

                hr;

                @if is_authenticated {
                    // Authenticated dashboard content
                    div.row {
                        div.col-md-3 {
                            div.card.mb-4 {
                                div.card-body.text-center {
                                    i.fas.fa-users.fa-3x.text-primary.mb-3 {}
                                    h5.card-title { "Customers" }
                                    p.card-text { "Manage your customer relationships" }
                                    a.btn.btn-primary href="/customers/" { "View Customers" }
                                }
                            }
                        }
                        div.col-md-3 {
                            div.card.mb-4 {
                                div.card-body.text-center {
                                    i.fas.fa-project-diagram.fa-3x.text-success.mb-3 {}
                                    h5.card-title { "Projects" }
                                    p.card-text { "Track and manage your projects" }
                                    a.btn.btn-success href="/projects/" { "View Projects" }
                                }
                            }
                        }
                        div.col-md-3 {
                            div.card.mb-4 {
                                div.card-body.text-center {
                                    i.fas.fa-handshake.fa-3x.text-info.mb-3 {}
                                    h5.card-title { "Teams" }
                                    p.card-text { "Collaborate with your team" }
                                    a.btn.btn-info href="/teams/" { "View Teams" }
                                }
                            }
                        }
                        div.col-md-3 {
                            div.card.mb-4 {
                                div.card-body.text-center {
                                    i.fas.fa-database.fa-3x.text-warning.mb-3 {}
                                    h5.card-title { "Database" }
                                    p.card-text { "Manage your business data" }
                                    a.btn.btn-warning href="/database/" { "View Database" }
                                }
                            }
                        }
                    }

                    // Recent activity section
                    div.row.mt-4 {
                        div.col-12 {
                            h3 { "Recent Activity" }
                            div.card {
                                div.card-body {
                                    p.text-muted { "No recent activity to display." }
                                    p { "Start by creating your first project or adding customers to see activity here." }
                                }
                            }
                        }
                    }
                } @else {
                    // Non-authenticated dashboard content
                    div.row {
                        div.col-md-8.mx-auto.text-center {
                            div.card {
                                div.card-body {
                                    h3.card-title { "Sign In Required" }
                                    p.card-text {
                                        "Please sign in to access your AppCove dashboard and manage your business data."
                                    }
                                    a.btn.btn-primary.btn-lg href="/auth/" {
                                        i.fab.fa-google.me-2 {}
                                        "Sign in with Google"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        });

        Response::HTML(doc.into())
    }
}
