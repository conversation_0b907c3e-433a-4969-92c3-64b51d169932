about-appcove {
    #page-heading {
        margin-bottom: 2rem;

        h1 {
            font-size: 3rem;
            color: #012650f8;
            margin-bottom: 1rem;
            line-height: 1.6;
            font-weight: bold;
        }

        p {
            font-size: 2rem;
            color: #00bfa5;
            margin-bottom: 1rem;
            line-height: 1.6;
            font-weight: 600;
        }

        strong {
            color: #012650f8;
            font-weight: bold;
        }

    }

    #page-body {
        margin-bottom: 2rem;

        p {
            font-size: 1.8rem;
            color: #012650f8;
            margin-bottom: 1rem;
            line-height: 1.6;
            font-weight: bold;
        }
    }

    #page-body > p {
        margin-top: 1rem;

        content{
            height: 1px;
            color: #6f868f;
            font-size: 3rem;
            line-height: 1.6;
            font-weight: bold;
        }
    }

    #page-footer {
        margin-top: 2rem;

        p {
            margin-bottom: 0.8rem;
            font-size: 1.2rem;
            color: #1f2527;
            line-height: 1.6;
            font-weight: bold;
        }

        strong {
            color: hwb(212 6% 53% / 0.973);
            font-weight: bold;
        }
        
        br {
            margin-bottom: 0.8rem;
        }    
    }
    #page-main {
        margin-top: 2rem;
        display: flex;
        flex-direction: column;
        gap: 2rem;

        header-2 {
            font-size: 2rem;
            color: #012650f8;
            line-height: 1.6;
            font-weight: bold;
        }

        p {
            font-size: 1rem;
            color: #8a8c8d;
            line-height: 1.6;
        }

        br {
            margin-bottom: 0.5rem;
        }

        header-3 {
            font-size: 2rem;
            color: #012650f8;
            line-height: 1.6;
            font-weight: bold;
        }
        
        hr {
            all: initial;
            display: block;
            border-bottom: 2.3px dotted rgb(9, 81, 129);
        }   
        .arrow-icon-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 3rem;
            height: 3rem;
            border-radius: 80%;
            background-color: #007b8a;
            margin-top: 2rem;
            margin-bottom: 3rem;
            cursor: pointer;
            transition: all 0.3s ease;

        &:hover {
            background-color: #0887a7;
        }

        .arrow-icon {
            color: white;
            font-size: 2rem;
            transition: all 0.3s ease;
        }
        }
    }
    #page-heading {
        margin-top: 2rem;
        margin-bottom: 2rem;

        h4 {
            font-size: 2rem;
            color:#15c9b1;
            margin-bottom: 1rem;
            line-height: 1.6;
            font-weight: bold;
        }
    }
    .staff-grid {
        display: flex;
        flex-wrap: wrap;
        margin-top: 1rem;
        margin-bottom: 1rem;
    }

    .staff-card {
        flex: 0 0 calc(25% - 2rem);
        background-color: lab(94.37% -0.71 3);
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        min-height: 80px;
        
        &:hover {
            background-color: #e6e6e6;
        }

        strong {
            font-size: 1.3rem;
            color: #1f2527;
            font-weight: bold;
            padding-left: 1.5rem;
        }

        span {
            font-size: 1.2rem;
            color: #353638f8;
            font-weight: bold;
            padding-left: 1.5rem;
        }

        em {
            font-size: 1rem;
            color:  #00bfa5;
            margin-bottom: 1rem;
            font-weight: bold;
            padding-left: 1.5rem;
        }
    }
    #page-footer {
        margin-top: 2rem;
        margin-bottom: 2rem;

        p {
            font-size: 1.2rem;
            color: #1f2527;
            line-height: 1.6;
            font-weight: bold;
        }

        strong {
            color:  hsl(224, 56%, 59%);
            font-weight: bold;
        }
    }
    #page-body {
        margin-top: 2rem;
        margin-bottom: 2rem;

        p {
            font-size: 1.2rem;
            color: #1f2527;
            line-height: 1.6;
            font-weight: bold;
        }

        strong  {
            color:  #012650f8;
            font-weight: bold;
        }
        strong  {
            color:  #00bfa5;
            font-weight: bold;
        }

        br {
            margin-bottom: 0.8rem;
            margin-top: 0.8rem;
        }
    }
        .timeline {
            h2 {
                font-size: 1.5rem;
                color: #012650f8;
                line-height: 1.6;
                font-weight: 1000;
                padding-left: 3rem;
                position: relative;
                background-color: rgb(235, 229, 218);    
            }
            p {
                font-size: 1rem;
                color: #1f2527;
                line-height: 1.6;
                font-weight: 700;
                padding-left: 3rem;
                position: relative;
                background-color: rgb(235, 229, 218);
            }
            
        }
        .timeline-list {
            list-style: none;
            padding: 5rem, 2rem;
            margin: 0 auto;
            position: relative;
            font-size: 1.2rem;
            margin-bottom: 5rem;
            color: #1f2527;
            line-height: 1.6;
            font-weight: bold;
            background-color: rgb(235, 229, 218);

            li {
                position: relative;
                padding-left: 2rem;
            }

            li:last-child {
                border-bottom: none;
                padding-bottom: 0;
            }

            .timeline-dot {
                position: absolute;
                left: 0;
                top: 0;
                width: 1rem;
                height: 1rem;
                background-color: #00bfa5;
                border-radius: 50%;
                border: 2px solid white;
                box-shadow: 0 0 0 2px #00bfa5;
            }

            .title {
                font-size: 1.2rem;
                color: #012650f8;
                line-height: 1.6;
                font-weight: bold;
            }
            .timeline-year {
                font-size: 1.2rem;
                color: #012650f8;
                line-height: 1.6;
                font-weight: bold;
            }
            .timeline-active-project {
                color: #00bfa5;
                font-weight: bold;
            }
            .timeline-description {
                font-size: 1.2rem;
                color: #1f2527;
                line-height: 1.6;
                font-weight: bold;
            }

            p {
                font-size: 1.2rem;
                color: #1f2527;
                line-height: 1.6;
                font-weight: bold;
            }
            hr {
                all: initial;
                display: vertical;
                border-bottom: 2.3px dotted rgb(9, 81, 129);
            }
        }
    }